<?php
/**
 * Admin Requests Management
 * Renamed from Prayer Requests to Requests
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

// Ensure prayer_responses table has is_admin_response column
try {
    $pdo->exec("ALTER TABLE prayer_responses ADD COLUMN is_admin_response TINYINT(1) DEFAULT 0");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['update_status'])) {
            // Update request status
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([
                $_POST['status'],
                $_POST['request_id']
            ]);
            
            $message = "Request status updated successfully!";
        }
        
        if (isset($_POST['delete_request'])) {
            // Delete request (admin only)
            $stmt = $pdo->prepare("DELETE FROM prayer_requests WHERE id = ?");
            $stmt->execute([$_POST['request_id']]);
            
            $message = "Request deleted successfully!";
        }
        
        if (isset($_POST['moderate_request'])) {
            // Moderate request (hide/show)
            $stmt = $pdo->prepare("
                UPDATE prayer_requests
                SET privacy_level = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            $stmt->execute([
                $_POST['new_privacy_level'],
                $_POST['request_id']
            ]);

            $message = "Request moderated successfully!";
        }

        if (isset($_POST['add_admin_response'])) {
            // Add admin response to request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_responses (prayer_request_id, member_id, response_type, comment, is_admin_response)
                VALUES (?, ?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE
                response_type = VALUES(response_type),
                comment = VALUES(comment),
                is_admin_response = 1,
                created_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([
                $_POST['request_id'],
                $_SESSION['admin_id'], // Use admin_id instead of member_id
                $_POST['response_type'] ?? 'support',
                $_POST['admin_comment']
            ]);

            // Optionally update request status to 'answered' if admin responds
            if (isset($_POST['mark_as_answered']) && $_POST['mark_as_answered'] == '1') {
                $stmt = $pdo->prepare("
                    UPDATE prayer_requests
                    SET status = 'answered', updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$_POST['request_id']]);
            }

            $message = "Admin response added successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';
$privacy_filter = $_GET['privacy'] ?? 'all';

// Build query with filters
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "pr.status = ?";
    $params[] = $status_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "pr.category = ?";
    $params[] = $category_filter;
}

if ($privacy_filter !== 'all') {
    $where_conditions[] = "pr.privacy_level = ?";
    $params[] = $privacy_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get all requests with member information
try {
    $stmt = $pdo->prepare("
        SELECT pr.*, m.full_name, m.email,
               COUNT(DISTINCT prr.id) as response_count,
               COUNT(DISTINCT CASE WHEN prr.is_admin_response = 1 THEN prr.id END) as admin_response_count,
               MAX(CASE WHEN prr.is_admin_response = 1 THEN prr.created_at END) as last_admin_response
        FROM prayer_requests pr
        JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        $where_clause
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
    ");
    $stmt->execute($params);
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $requests = [];
    $error = "Error loading requests: " . $e->getMessage();
}

// Get statistics
$stats = [
    'total' => 0,
    'active' => 0,
    'answered' => 0,
    'closed' => 0,
    'urgent' => 0
];

try {
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed,
            SUM(CASE WHEN is_urgent = 1 THEN 1 ELSE 0 END) as urgent
        FROM prayer_requests
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Keep default stats
}

$page_title = "Requests Management";
$page_header = "Requests Management";
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-chat-heart"></i> Requests Management</h1>
</div>

            <?php if (isset($message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                            <p class="card-text small">Total Requests</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?php echo $stats['active']; ?></h5>
                            <p class="card-text small">Active</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo $stats['answered']; ?></h5>
                            <p class="card-text small">Answered</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-secondary"><?php echo $stats['closed']; ?></h5>
                            <p class="card-text small">Closed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger"><?php echo $stats['urgent']; ?></h5>
                            <p class="card-text small">Urgent</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Statuses</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="answered" <?php echo $status_filter === 'answered' ? 'selected' : ''; ?>>Answered</option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>All Categories</option>
                                <option value="personal" <?php echo $category_filter === 'personal' ? 'selected' : ''; ?>>Personal</option>
                                <option value="family" <?php echo $category_filter === 'family' ? 'selected' : ''; ?>>Family</option>
                                <option value="health" <?php echo $category_filter === 'health' ? 'selected' : ''; ?>>Health</option>
                                <option value="work" <?php echo $category_filter === 'work' ? 'selected' : ''; ?>>Work</option>
                                <option value="ministry" <?php echo $category_filter === 'ministry' ? 'selected' : ''; ?>>Ministry</option>
                                <option value="community" <?php echo $category_filter === 'community' ? 'selected' : ''; ?>>Community</option>
                                <option value="other" <?php echo $category_filter === 'other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="privacy" class="form-label">Privacy</label>
                            <select class="form-select" id="privacy" name="privacy">
                                <option value="all" <?php echo $privacy_filter === 'all' ? 'selected' : ''; ?>>All Privacy Levels</option>
                                <option value="private" <?php echo $privacy_filter === 'private' ? 'selected' : ''; ?>>Private</option>
                                <option value="members" <?php echo $privacy_filter === 'members' ? 'selected' : ''; ?>>Members</option>
                                <option value="public" <?php echo $privacy_filter === 'public' ? 'selected' : ''; ?>>Public</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-funnel"></i> Filter
                                </button>
                                <a href="requests.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Requests List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list"></i> Requests (<?php echo count($requests); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($requests)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-chat-heart text-muted" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">No Requests Found</h4>
                            <p class="text-muted">No requests match your current filters.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Privacy</th>
                                        <th>Status</th>
                                        <th>Responses</th>
                                        <th>Admin Response</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($request['full_name']); ?></strong>
                                            <?php if ($request['is_anonymous']): ?>
                                                <br><small class="text-muted"><i class="bi bi-incognito"></i> Anonymous</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($request['title']); ?></strong>
                                            <?php if ($request['is_urgent']): ?>
                                                <span class="badge bg-danger ms-1">Urgent</span>
                                            <?php endif; ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($request['description'], 0, 100)); ?>...</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo ucfirst($request['category']); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $request['privacy_level'] === 'private' ? 'dark' :
                                                     ($request['privacy_level'] === 'members' ? 'primary' : 'success');
                                            ?>"><?php echo ucfirst($request['privacy_level']); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php
                                                echo $request['status'] === 'active' ? 'warning' :
                                                     ($request['status'] === 'answered' ? 'success' : 'secondary');
                                            ?>"><?php echo ucfirst($request['status']); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $request['response_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($request['admin_response_count'] > 0): ?>
                                                <span class="badge bg-success"><?php echo $request['admin_response_count']; ?> Admin</span>
                                                <br><small class="text-muted">Last: <?php echo date('M j', strtotime($request['last_admin_response'])); ?></small>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">No Response</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($request['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-success btn-sm" onclick="showResponseModal(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title']); ?>', '<?php echo htmlspecialchars($request['full_name']); ?>')" title="Respond">
                                                    <i class="bi bi-chat-dots"></i>
                                                </button>
                                                <button class="btn btn-outline-primary btn-sm" onclick="updateStatus(<?php echo $request['id']; ?>, '<?php echo $request['status']; ?>')" title="Update Status">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title']); ?>')" title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

<!-- Admin Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="responseModalLabel">
                    <i class="bi bi-chat-dots"></i> Respond to Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="request_id" id="modal_request_id">
                    <input type="hidden" name="add_admin_response" value="1">

                    <div class="mb-3">
                        <label class="form-label"><strong>Request:</strong></label>
                        <div id="modal_request_title" class="text-muted"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label"><strong>From:</strong></label>
                        <div id="modal_member_name" class="text-muted"></div>
                    </div>

                    <div class="mb-3">
                        <label for="response_type" class="form-label">Response Type</label>
                        <select class="form-select" id="response_type" name="response_type" required>
                            <option value="support">Support & Encouragement</option>
                            <option value="guidance">Guidance & Advice</option>
                            <option value="prayer">Prayer Response</option>
                            <option value="referral">Referral to Resources</option>
                            <option value="follow_up">Follow-up Required</option>
                            <option value="resolved">Issue Resolved</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="admin_comment" class="form-label">Your Response</label>
                        <textarea class="form-control" id="admin_comment" name="admin_comment" rows="5"
                                  placeholder="Write your detailed response to this request..." required></textarea>
                        <div class="form-text">This response will be visible to the member who submitted the request.</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="mark_as_answered" name="mark_as_answered" value="1">
                            <label class="form-check-label" for="mark_as_answered">
                                Mark this request as "Answered"
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send"></i> Send Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showResponseModal(requestId, requestTitle, memberName) {
    document.getElementById('modal_request_id').value = requestId;
    document.getElementById('modal_request_title').textContent = requestTitle;
    document.getElementById('modal_member_name').textContent = memberName;

    // Clear previous values
    document.getElementById('response_type').value = 'support';
    document.getElementById('admin_comment').value = '';
    document.getElementById('mark_as_answered').checked = false;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('responseModal'));
    modal.show();
}

function updateStatus(requestId, currentStatus) {
    const newStatus = prompt(`Current status: ${currentStatus}\nEnter new status (active, answered, closed):`, currentStatus);
    if (newStatus && ['active', 'answered', 'closed'].includes(newStatus)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="request_id" value="${requestId}">
            <input type="hidden" name="status" value="${newStatus}">
            <input type="hidden" name="update_status" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteRequest(requestId, title) {
    if (confirm(`Are you sure you want to delete the request "${title}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="request_id" value="${requestId}">
            <input type="hidden" name="delete_request" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
