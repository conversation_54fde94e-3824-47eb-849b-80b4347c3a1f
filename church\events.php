<?php
require_once __DIR__ . '/config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Get events
$search = $_GET['search'] ?? '';
$where_conditions = ["is_active = 1"]; // Only show active events
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ? OR location LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

$sql = "
    SELECT e.*,
           (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') as attending_count
    FROM events e
    $where_clause
    ORDER BY e.event_date ASC
";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upcoming Events - <?php echo htmlspecialchars(get_organization_name()); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/frontend_css.php'; ?>

    <style>
        .event-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: var(--bs-border-radius, 0.375rem);
        }
        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .event-date {
            background: linear-gradient(135deg, var(--bs-primary, #6f42c1), var(--bs-secondary, #5a32a3));
            color: white;
            border-radius: var(--bs-border-radius, 0.375rem);
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
        }
        .event-date .day {
            font-size: 2rem;
            font-weight: bold;
            line-height: 1;
        }
        .event-date .month {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .event-date .time {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .hero-section {
            background: linear-gradient(135deg, var(--bs-primary, #6f42c1), var(--bs-secondary, #5a32a3));
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .capacity-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 5px 10px;
            border-radius: var(--bs-border-radius, 0.375rem);
            font-size: 0.8rem;
        }
        .event-location {
            color: var(--bs-secondary, #6c757d);
            font-size: 0.9rem;
        }
        .btn-primary {
            background-color: var(--bs-primary, #007bff);
            border-color: var(--bs-primary, #007bff);
        }
        .btn-success {
            background-color: var(--bs-success, #28a745);
            border-color: var(--bs-success, #28a745);
        }
        .search-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: var(--bs-border-radius, 0.375rem);
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'navbar.php'; ?>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1 class="display-4 mb-3">Upcoming Events</h1>
                    <p class="lead">Join us for these exciting events and activities at <?php echo get_organization_name(); ?></p>
                </div>
                <div class="col-md-4">
                    <form method="GET" class="d-flex">
                        <input type="text" class="form-control me-2" name="search" 
                               placeholder="Search events..." value="<?= htmlspecialchars($search) ?>">
                        <button type="submit" class="btn btn-light">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Section -->
    <div class="container">
        <?php if (empty($events)): ?>
            <div class="row">
                <div class="col-md-12 text-center">
                    <div class="py-5">
                        <i class="bi bi-calendar-x display-1 text-muted"></i>
                        <h3 class="mt-3">No Events Found</h3>
                        <p class="text-muted">
                            <?php if (!empty($search)): ?>
                                No events match your search criteria. <a href="events.php">View all events</a>
                            <?php else: ?>
                                There are no upcoming events at this time. Please check back later.
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($events as $event): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card event-card h-100">
                            <div class="card-body position-relative">
                                <!-- Capacity Badge -->
                                <?php if ($event['max_attendees']): ?>
                                    <div class="capacity-badge">
                                        <i class="bi bi-people"></i> 
                                        <?= $event['attending_count'] ?>/<?= $event['max_attendees'] ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Event Date -->
                                <div class="event-date">
                                    <div class="day"><?= date('j', strtotime($event['event_date'])) ?></div>
                                    <div class="month"><?= date('M Y', strtotime($event['event_date'])) ?></div>
                                    <div class="time"><?= date('g:i A', strtotime($event['event_date'])) ?></div>
                                </div>
                                
                                <!-- Event Details -->
                                <h5 class="card-title"><?= htmlspecialchars($event['title']) ?></h5>
                                
                                <?php if ($event['location']): ?>
                                    <p class="event-location">
                                        <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($event['location']) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <p class="card-text">
                                    <?= htmlspecialchars(substr($event['description'], 0, 120)) ?>
                                    <?php if (strlen($event['description']) > 120): ?>...<?php endif; ?>
                                </p>
                                
                                <!-- Action Buttons -->
                                <div class="mt-auto">
                                    <?php
                                    // Generate URL-friendly slug
                                    function generateEventSlug($title) {
                                        $slug = strtolower($title);
                                        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
                                        $slug = preg_replace('/[\s-]+/', '-', $slug);
                                        return trim($slug, '-');
                                    }
                                    $event_slug = generateEventSlug($event['title']);
                                    ?>
                                    <a href="event_detail.php?id=<?= $event_slug ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-info-circle"></i> View Details
                                    </a>
                                    
                                    <?php 
                                    $is_full = $event['max_attendees'] && $event['attending_count'] >= $event['max_attendees'];
                                    $is_past = strtotime($event['event_date']) < time();
                                    ?>
                                    
                                    <?php if (!$is_past && !$is_full): ?>
                                        <button class="btn btn-success btn-sm" onclick="rsvpEvent(<?= $event['id'] ?>)">
                                            <i class="bi bi-check-circle"></i> RSVP
                                        </button>
                                    <?php elseif ($is_full): ?>
                                        <span class="badge bg-warning">Event Full</span>
                                    <?php elseif ($is_past): ?>
                                        <span class="badge bg-secondary">Past Event</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="text-muted mb-0">&copy; <?= date('Y') ?> <?php echo get_organization_name(); ?>. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- RSVP Modal -->
    <div class="modal fade" id="rsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">RSVP for Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rsvpForm">
                        <input type="hidden" id="rsvp_event_id" name="event_id">
                        
                        <div class="mb-3">
                            <label for="guest_name" class="form-label">Your Name *</label>
                            <input type="text" class="form-control" id="guest_name" name="guest_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="guest_phone" name="guest_phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="party_size" class="form-label">Party Size</label>
                            <select class="form-select" id="party_size" name="party_size">
                                <option value="1">1 Person</option>
                                <option value="2">2 People</option>
                                <option value="3">3 People</option>
                                <option value="4">4 People</option>
                                <option value="5">5+ People</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function rsvpEvent(eventId) {
            document.getElementById('rsvp_event_id').value = eventId;
            new bootstrap.Modal(document.getElementById('rsvpModal')).show();
        }
        
        function submitRSVP() {
            const form = document.getElementById('rsvpForm');
            const formData = new FormData(form);
            formData.append('action', 'rsvp');
            
            fetch('rsvp_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('RSVP submitted successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred. Please try again.');
            });
        }
    </script>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo htmlspecialchars(get_organization_name()); ?></h5>
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="index.html" class="text-light me-3">Home</a>
                    <a href="events.php" class="text-light me-3">Events</a>
                    <a href="enhanced_donate.php" class="text-light">Donate</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
