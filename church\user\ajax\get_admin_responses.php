<?php
/**
 * AJAX endpoint to get admin responses for a specific request
 */

session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit();
}

// Check if request_id is provided
if (!isset($_POST['request_id']) || !is_numeric($_POST['request_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid request ID']);
    exit();
}

$request_id = (int)$_POST['request_id'];
$user_id = $_SESSION['user_id'];

try {
    // First, verify that this request belongs to the current user
    $stmt = $pdo->prepare("SELECT id FROM prayer_requests WHERE id = ? AND member_id = ?");
    $stmt->execute([$request_id, $user_id]);
    
    if (!$stmt->fetch()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit();
    }
    
    // Get admin responses for this request
    $stmt = $pdo->prepare("
        SELECT pr.response_type, pr.comment, pr.created_at
        FROM prayer_responses pr
        WHERE pr.prayer_request_id = ? 
        AND pr.is_admin_response = 1
        ORDER BY pr.created_at DESC
    ");
    $stmt->execute([$request_id]);
    $responses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'responses' => $responses
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error']);
}
?>
