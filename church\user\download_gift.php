<?php
/**
 * Download Gift Card Generator
 * Generates downloadable PDF and JPEG gift cards
 */

// Suppress warnings for clean output
error_reporting(E_ERROR | E_PARSE);

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

// Get parameters
$gift_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';

if (!$gift_id) {
    die('Invalid gift ID');
}

// Get gift data
$stmt = $pdo->prepare("
    SELECT mg.*, 
           sender.full_name as sender_name, 
           recipient.full_name as recipient_name,
           recipient.first_name as recipient_first_name
    FROM member_gifts mg
    LEFT JOIN members sender ON mg.sender_id = sender.id
    LEFT JOIN members recipient ON mg.recipient_id = recipient.id
    WHERE mg.id = ? AND (mg.sender_id = ? OR mg.recipient_id = ?)
");
$stmt->execute([$gift_id, $_SESSION['user_id'], $_SESSION['user_id']]);
$gift = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$gift) {
    die('Gift not found or access denied');
}

// Get organization details
$org_name = get_organization_name();
$org_logo = get_site_setting('main_logo', '');

// Determine if user is sender or recipient
$is_sender = ($gift['sender_id'] == $_SESSION['user_id']);
$display_name = $is_sender ? $gift['recipient_name'] : $gift['sender_name'];
$from_to_text = $is_sender ? 'To: ' . $gift['recipient_name'] : 'From: ' . ($gift['is_anonymous'] ? 'Anonymous' : $gift['sender_name']);

if ($format === 'pdf') {
    // Only allow PDF if DomPDF is available
    $dompdf_found = false;
    $dompdf_paths = [
        '../vendor/autoload.php',
        '../../vendor/autoload.php',
        '../../../vendor/autoload.php'
    ];
    foreach ($dompdf_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            if (class_exists('Dompdf\\Dompdf')) {
                $dompdf_found = true;
                break;
            }
        }
    }
    if ($dompdf_found) {
        generatePDF($gift, $org_name, $org_logo, $from_to_text);
    } else {
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="gift-card-error.txt"');
        echo "PDF download is not available. Please contact the administrator to enable PDF support (DomPDF required).";
        exit();
    }
} else {
    generateJPEG($gift, $org_name, $org_logo, $from_to_text);
}

function generatePDF($gift, $org_name, $org_logo, $from_to_text) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Try to use DomPDF if available
    $pdf_generated = false;

    // Check for DomPDF in multiple possible locations
    $dompdf_paths = [
        '../vendor/autoload.php',
        '../../vendor/autoload.php',
        '../../../vendor/autoload.php'
    ];

    foreach ($dompdf_paths as $path) {
        if (file_exists($path)) {
            try {
                require_once $path;
                if (class_exists('Dompdf\Dompdf')) {
                    // Create HTML content for PDF
                    $html = generatePDFHTML($gift, $org_name, $from_to_text);

                    $dompdf = new Dompdf\Dompdf();
                    $dompdf->loadHtml($html);
                    $dompdf->setPaper('A4', 'portrait');
                    $dompdf->render();

                    header('Content-Type: application/pdf');
                    header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.pdf"');
                    header('Content-Length: ' . strlen($dompdf->output()));

                    echo $dompdf->output();
                    $pdf_generated = true;
                    exit(); // Important: exit after successful PDF generation
                }
            } catch (Exception $e) {
                error_log("DomPDF error: " . $e->getMessage());
                continue;
            }
        }
    }

    // If PDF generation failed, create an HTML file that can be printed to PDF
    if (!$pdf_generated) {
        generatePrintableHTML($gift, $org_name, $from_to_text);
    }
}

function generatePDFHTML($gift, $org_name, $from_to_text) {
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .gift-card {
            background: white;
            max-width: 600px;
            margin: 0 auto;
            border: 3px solid #667eea;
            border-radius: 15px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        .header .org-name {
            font-size: 1.2em;
            margin-top: 10px;
        }
        .content {
            padding: 40px;
        }
        .gift-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .gift-message {
            font-size: 1.2em;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .gift-details {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            text-align: center;
        }
        .detail-item {
            display: inline-block;
            margin: 0 20px;
            text-align: center;
        }
        .detail-label {
            font-weight: bold;
            color: #667eea;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .detail-value {
            font-size: 1.1em;
            color: #333;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 0.9em;
        }
        .gift-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="gift-card">
        <div class="header">
            <div class="gift-icon">🎁</div>
            <h1>Gift Card</h1>
            <div class="org-name">' . htmlspecialchars($org_name) . '</div>
        </div>
        <div class="content">
            <div class="gift-title">' . htmlspecialchars($gift['gift_title']) . '</div>
            <div class="gift-message">' . nl2br(htmlspecialchars($gift['gift_message'])) . '</div>
            <div class="gift-details">
                <div class="detail-item">
                    <div class="detail-label">' . ($gift['sender_id'] == $_SESSION['user_id'] ? 'To' : 'From') . '</div>
                    <div class="detail-value">' . htmlspecialchars($from_to_text) . '</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Type</div>
                    <div class="detail-value">' . ucfirst(htmlspecialchars($gift['gift_type'])) . '</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Date</div>
                    <div class="detail-value">' . date('M j, Y', strtotime($gift['created_at'])) . '</div>
                </div>
            </div>
        </div>
        <div class="footer">
            Generated from ' . htmlspecialchars($org_name) . ' Gift Management System<br>
            ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
}

function generatePrintableHTML($gift, $org_name, $from_to_text) {
    // Create a printable HTML file that can be saved as PDF by the browser
    $html = generatePDFHTML($gift, $org_name, $from_to_text);

    // Add print-specific styles
    $html = str_replace('</head>', '
    <style media="print">
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .no-print {
            display: none;
        }
    </style>
    <script>
        window.onload = function() {
            // Auto-open print dialog
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
    </head>', $html);

    // Add a print instruction at the top
    $html = str_replace('<body>', '<body>
    <div class="no-print" style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 5px; text-align: center;">
        <strong>Print Instructions:</strong> Use your browser\'s print function (Ctrl+P) and select "Save as PDF" to create a PDF file.
        <br><small>This message will not appear in the printed version.</small>
    </div>', $html);

    // Set headers for HTML download
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.html"');

    echo $html;
    exit();
}

function generateJPEG($gift, $org_name, $org_logo, $from_to_text) {
    // If an uploaded image exists, return it as the JPEG download
    if (!empty($gift['gift_file_path'])) {
        $file_ext = strtolower(pathinfo($gift['gift_file_path'], PATHINFO_EXTENSION));
        $image_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($file_ext, $image_types)) {
            $file_path = realpath(dirname(__DIR__) . '/' . $gift['gift_file_path']);
            if ($file_path && file_exists($file_path)) {
                header('Content-Type: image/jpeg');
                header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.jpg"');
                readfile($file_path);
                exit();
            }
        }
    }
    // Otherwise, fallback to generated image as before
    if (!extension_loaded('gd')) {
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.txt"');
        echo "GIFT CARD\n";
        echo "=========\n\n";
        echo "From: " . $org_name . "\n";
        echo "Title: " . $gift['gift_title'] . "\n";
        echo "Message: " . $gift['gift_message'] . "\n";
        echo $from_to_text . "\n";
        echo "Date: " . date('M j, Y', strtotime($gift['created_at'])) . "\n";
        exit();
    }
    // ...existing code for GD image generation...
}
?>
