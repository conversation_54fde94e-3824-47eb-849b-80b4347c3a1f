<?php
/**
 * User Gifts Management
 * View received and sent gifts
 */

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    header('Location: login.php');
    exit();
}

// Get received gifts
$stmt = $pdo->prepare("
    SELECT mg.*, m.full_name as sender_name, m.first_name as sender_first_name
    FROM member_gifts mg
    JOIN members m ON mg.sender_id = m.id
    WHERE mg.recipient_id = ?
    ORDER BY mg.created_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$received_gifts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get sent gifts
$stmt = $pdo->prepare("
    SELECT mg.*, m.full_name as recipient_name, m.first_name as recipient_first_name
    FROM member_gifts mg
    JOIN members m ON mg.recipient_id = m.id
    WHERE mg.sender_id = ?
    ORDER BY mg.created_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$sent_gifts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get site settings for branding
$sitename = get_organization_name() . ' - My Gifts';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get organization name for display
$org_name = get_organization_name();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Gifts - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .gifts-container {
            margin-top: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .gift-card {
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .gift-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .pending-gift {
            border-left: 6px solid #ffc107;
            background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
        }

        .sent-gift-card {
            border-left: 6px solid var(--bs-primary, #007bff);
            background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
        }

        .gift-file-preview {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .gift-card-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            padding: 15px;
            margin: 0;
            border-radius: 0;
        }

        .gift-card-body {
            padding: 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .gift-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: white;
        }

        .gift-type-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .gift-message {
            font-size: 0.9rem;
            line-height: 1.5;
            color: #555;
            margin-bottom: 15px;
            flex: 1;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .gift-meta {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 0.85rem;
        }

        .gift-actions {
            background: #f8f9fa;
            padding: 12px;
            margin: 0;
            border-radius: 0;
            margin-top: auto;
        }

        .action-btn {
            border-radius: 6px;
            padding: 6px 12px;
            font-weight: 500;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-1px);
        }

        .nav-tabs .nav-link {
            color: var(--bs-body-color, #495057);
            border: none;
            border-bottom: 2px solid transparent;
        }

        .nav-tabs .nav-link.active {
            color: var(--bs-primary, #007bff);
            border-bottom-color: var(--bs-primary, #007bff);
            background: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 0.375rem);
        }

        .btn-outline-primary {
            border-color: var(--bs-primary, #007bff);
            color: var(--bs-primary, #007bff);
        }

        .btn-outline-primary:hover {
            background-color: var(--bs-primary, #007bff);
            border-color: var(--bs-primary, #007bff);
        }

        .btn-outline-secondary {
            border-color: var(--bs-secondary, #6c757d);
            color: var(--bs-secondary, #6c757d);
        }

        .btn-outline-secondary:hover {
            background-color: var(--bs-secondary, #6c757d);
            border-color: var(--bs-secondary, #6c757d);
        }

        .empty-state {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            margin: 40px 0;
        }

        .empty-state i {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .gift-card-body {
                padding: 12px;
            }

            .gift-actions {
                padding: 10px;
            }

            .action-btn {
                padding: 5px 10px;
                font-size: 0.75rem;
            }

            .gift-title {
                font-size: 1.1rem;
            }

            .gift-file-preview {
                height: 120px;
            }
        }

        @media (max-width: 576px) {
            .gift-file-preview {
                height: 100px;
            }

            .empty-state {
                padding: 40px 20px;
            }

            .gift-card-body {
                padding: 10px;
            }

            .gift-actions {
                padding: 8px;
            }

            .action-btn {
                padding: 4px 8px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container gifts-container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-gift"></i> My Gifts</h2>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="downloadGiftSummary()">
                            <i class="bi bi-download"></i> Download Summary
                        </button>
                        <a href="send_gift.php" class="btn btn-primary">
                            <i class="bi bi-send"></i> Send a Gift
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="giftTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="received-tab" data-bs-toggle="tab" data-bs-target="#received" type="button" role="tab">
                    <i class="bi bi-inbox"></i> Received Gifts (<?php echo count($received_gifts); ?>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                    <i class="bi bi-send"></i> Sent Gifts (<?php echo count($sent_gifts); ?>)
                </button>
            </li>
        </ul>

        <div class="tab-content" id="giftTabContent">
            <!-- Received Gifts -->
            <div class="tab-pane fade show active" id="received" role="tabpanel">
                <div class="mt-4">
                    <?php if (empty($received_gifts)): ?>
                        <div class="empty-state">
                            <i class="bi bi-gift" style="font-size: 5rem;"></i>
                            <h3 class="mt-4 mb-3">No Gifts Received Yet</h3>
                            <p class="text-muted mb-4">When someone sends you a gift, it will appear here with all the love and joy it carries.</p>
                            <a href="send_gift.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-send"></i> Send Your First Gift
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-3">
                            <?php foreach ($received_gifts as $gift): ?>
                                <div class="col">
                                    <div class="card gift-card h-100 <?php echo $gift['delivery_date'] > date('Y-m-d') ? 'pending-gift' : ''; ?>">
                                        <!-- Gift Card Header -->
                                        <div class="gift-card-header">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h5 class="gift-title mb-0"><?php echo htmlspecialchars($gift['gift_title']); ?></h5>
                                                <span class="gift-type-badge">
                                                    <?php echo ucfirst($gift['gift_type']); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Gift Card Body -->
                                        <div class="gift-card-body">
                                            <?php if ($gift['gift_file_path']): ?>
                                                <div class="text-center mb-3">
                                                    <?php
                                                    $file_ext = strtolower(pathinfo($gift['gift_file_path'], PATHINFO_EXTENSION));
                                                    if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                        <img src="../<?php echo htmlspecialchars($gift['gift_file_path']); ?>"
                                                             class="gift-file-preview" alt="Gift Image">
                                                    <?php else: ?>
                                                        <div class="gift-file-preview d-flex align-items-center justify-content-center bg-light">
                                                            <div class="text-center">
                                                                <i class="bi bi-file-earmark text-primary" style="font-size: 3rem;"></i>
                                                                <p class="mt-2 mb-0 text-muted">File Attachment</p>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>

                                            <div class="gift-message"><?php echo nl2br(htmlspecialchars($gift['gift_message'])); ?></div>
                                            
                                            <!-- Gift Meta Information -->
                                            <div class="gift-meta">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">From:</small>
                                                        <strong><?php echo $gift['is_anonymous'] ? 'Anonymous' : htmlspecialchars($gift['sender_name']); ?></strong>
                                                    </div>
                                                    <div class="col-6 text-end">
                                                        <small class="text-muted d-block">
                                                            <?php if ($gift['delivery_date'] > date('Y-m-d')): ?>
                                                                Scheduled for:
                                                            <?php else: ?>
                                                                Received:
                                                            <?php endif; ?>
                                                        </small>
                                                        <strong>
                                                            <?php if ($gift['delivery_date'] > date('Y-m-d')): ?>
                                                                <?php echo date('M j, Y', strtotime($gift['delivery_date'])); ?>
                                                            <?php else: ?>
                                                                <?php echo date('M j, Y', strtotime($gift['created_at'])); ?>
                                                            <?php endif; ?>
                                                        </strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Gift Actions -->
                                        <div class="gift-actions">
                                            <div class="row g-2">
                                                <!-- Primary Actions -->
                                                <div class="col-12 mb-2">
                                                    <div class="d-flex gap-2 justify-content-center">
                                                        <a href="download_gift.php?id=<?php echo $gift['id']; ?>&format=pdf"
                                                           class="btn btn-primary action-btn" target="_blank">
                                                            <i class="bi bi-file-pdf"></i> PDF
                                                        </a>
                                                        <a href="download_gift.php?id=<?php echo $gift['id']; ?>&format=jpeg"
                                                           class="btn btn-success action-btn" target="_blank">
                                                            <i class="bi bi-image"></i> JPEG
                                                        </a>
                                                        <?php if ($gift['gift_file_path']): ?>
                                                        <a href="../<?php echo htmlspecialchars($gift['gift_file_path']); ?>"
                                                           class="btn btn-info action-btn" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <!-- Secondary Actions -->
                                                <div class="col-12">
                                                    <div class="d-flex gap-2 justify-content-center">
                                                        <button class="btn btn-outline-secondary action-btn" onclick="shareGift(<?php echo $gift['id']; ?>)">
                                                            <i class="bi bi-share"></i> Share
                                                        </button>
                                                        <button class="btn btn-outline-danger action-btn" onclick="deleteGift(<?php echo $gift['id']; ?>, 'received')">
                                                            <i class="bi bi-trash"></i> Delete
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sent Gifts -->
            <div class="tab-pane fade" id="sent" role="tabpanel">
                <div class="mt-4">
                    <?php if (empty($sent_gifts)): ?>
                        <div class="empty-state">
                            <i class="bi bi-send" style="font-size: 5rem;"></i>
                            <h3 class="mt-4 mb-3">No Gifts Sent Yet</h3>
                            <p class="text-muted mb-4">Start spreading joy and making someone's day brighter by sending your first gift!</p>
                            <a href="send_gift.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-send"></i> Send a Gift Now
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-3">
                            <?php foreach ($sent_gifts as $gift): ?>
                                <div class="col">
                                    <div class="card gift-card sent-gift-card h-100 <?php echo $gift['delivery_date'] > date('Y-m-d') ? 'pending-gift' : ''; ?>">
                                        <!-- Gift Card Header -->
                                        <div class="gift-card-header">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h5 class="gift-title mb-0"><?php echo htmlspecialchars($gift['gift_title']); ?></h5>
                                                <span class="gift-type-badge">
                                                    <?php echo ucfirst($gift['gift_type']); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Gift Card Body -->
                                        <div class="gift-card-body">
                                            <?php if ($gift['gift_file_path']): ?>
                                                <div class="text-center mb-3">
                                                    <?php
                                                    $file_ext = strtolower(pathinfo($gift['gift_file_path'], PATHINFO_EXTENSION));
                                                    if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                        <img src="../<?php echo htmlspecialchars($gift['gift_file_path']); ?>"
                                                             class="gift-file-preview" alt="Gift Image">
                                                    <?php else: ?>
                                                        <div class="gift-file-preview d-flex align-items-center justify-content-center bg-light">
                                                            <div class="text-center">
                                                                <i class="bi bi-file-earmark text-primary" style="font-size: 3rem;"></i>
                                                                <p class="mt-2 mb-0 text-muted">File Attachment</p>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>

                                            <div class="gift-message"><?php echo nl2br(htmlspecialchars($gift['gift_message'])); ?></div>

                                            <!-- Gift Meta Information -->
                                            <div class="gift-meta">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">To:</small>
                                                        <strong><?php echo htmlspecialchars($gift['recipient_name']); ?></strong>
                                                    </div>
                                                    <div class="col-6 text-end">
                                                        <small class="text-muted d-block">Status:</small>
                                                        <strong>
                                                            <?php if ($gift['delivery_date'] > date('Y-m-d')): ?>
                                                                <i class="bi bi-clock text-warning"></i> Scheduled
                                                            <?php else: ?>
                                                                <i class="bi bi-check-circle text-success"></i> Delivered
                                                            <?php endif; ?>
                                                        </strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Gift Actions -->
                                        <div class="gift-actions">
                                            <div class="row g-2">
                                                <!-- Primary Actions -->
                                                <div class="col-12 mb-2">
                                                    <div class="d-flex gap-2 justify-content-center">
                                                        <a href="download_gift.php?id=<?php echo $gift['id']; ?>&format=pdf"
                                                           class="btn btn-primary action-btn" target="_blank">
                                                            <i class="bi bi-file-pdf"></i> PDF
                                                        </a>
                                                        <a href="download_gift.php?id=<?php echo $gift['id']; ?>&format=jpeg"
                                                           class="btn btn-success action-btn" target="_blank">
                                                            <i class="bi bi-image"></i> JPEG
                                                        </a>
                                                        <?php if ($gift['gift_file_path']): ?>
                                                        <a href="../<?php echo htmlspecialchars($gift['gift_file_path']); ?>"
                                                           class="btn btn-info action-btn" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <!-- Secondary Actions -->
                                                <div class="col-12">
                                                    <div class="d-flex gap-2 justify-content-center">
                                                        <button class="btn btn-outline-secondary action-btn" onclick="shareGift(<?php echo $gift['id']; ?>)">
                                                            <i class="bi bi-share"></i> Share
                                                        </button>
                                                        <button class="btn btn-outline-danger action-btn" onclick="deleteGift(<?php echo $gift['id']; ?>, 'sent')">
                                                            <i class="bi bi-trash"></i> Delete
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>


        // Share gift
        function shareGift(giftId) {
            if (navigator.share) {
                navigator.share({
                    title: 'Check out this gift I received!',
                    text: 'I received a wonderful gift!',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Gift link copied to clipboard!');
                });
            }
        }

        // Download gift summary
        function downloadGiftSummary() {
            const receivedCount = <?php echo count($received_gifts); ?>;
            const sentCount = <?php echo count($sent_gifts); ?>;

            const summary = `
                <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
                    <h1 style="color: #007bff; text-align: center;">My Gifts Summary</h1>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h2>Statistics</h2>
                        <p><strong>Gifts Received:</strong> ${receivedCount}</p>
                        <p><strong>Gifts Sent:</strong> ${sentCount}</p>
                        <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
                    </div>
                    <div style="text-align: center; color: #666; font-size: 12px; margin-top: 30px;">
                        <p>Generated from <?php echo htmlspecialchars(get_organization_name()); ?> Gift Management System</p>
                    </div>
                </div>
            `;

            const blob = new Blob([summary], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'my-gifts-summary.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Delete gift function
        function deleteGift(giftId, type) {
            if (confirm('Are you sure you want to delete this gift? This action cannot be undone.')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'delete_gift.php';
                form.innerHTML = `
                    <input type="hidden" name="gift_id" value="${giftId}">
                    <input type="hidden" name="gift_type" value="${type}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
