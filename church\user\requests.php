<?php
/**
 * User Requests Management
 * Renamed from Prayer Requests to Requests
 */

require_once '../config.php';

// Ensure prayer_responses table has is_admin_response column
try {
    // Check if column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'is_admin_response'");
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $pdo->exec("ALTER TABLE prayer_responses ADD COLUMN is_admin_response TINYINT(1) DEFAULT 0");
    }
} catch (PDOException $e) {
    // If there's an error, we'll handle it in the queries below
    error_log("Error checking/adding is_admin_response column: " . $e->getMessage());
}

// Check if the column exists for our queries
$has_admin_response_column = false;
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'is_admin_response'");
    $has_admin_response_column = ($stmt->rowCount() > 0);
} catch (PDOException $e) {
    $has_admin_response_column = false;
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['create_request'])) {
            // Create new request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_requests (member_id, title, description, category, privacy_level, is_urgent, is_anonymous)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['privacy_level'],
                isset($_POST['is_urgent']) ? 1 : 0,
                isset($_POST['is_anonymous']) ? 1 : 0
            ]);
            
            $message = "Request submitted successfully!";
        }
        
        if (isset($_POST['update_status'])) {
            // Update request status
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET status = ?, answered_description = ?, answered_date = ?
                WHERE id = ? AND member_id = ?
            ");
            
            $answered_date = $_POST['status'] === 'answered' ? date('Y-m-d H:i:s') : null;
            
            $stmt->execute([
                $_POST['status'],
                $_POST['answered_description'] ?? null,
                $answered_date,
                $_POST['request_id'],
                $_SESSION['user_id']
            ]);
            
            $message = "Request status updated successfully!";
        }
        
        if (isset($_POST['add_response'])) {
            // Add response to someone's request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_responses (prayer_request_id, member_id, response_type, comment)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                response_type = VALUES(response_type), 
                comment = VALUES(comment),
                created_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([
                $_POST['request_id'],
                $_SESSION['user_id'],
                $_POST['response_type'],
                $_POST['comment']
            ]);
            
            $message = "Response added successfully!";
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get user's own requests
if ($has_admin_response_column) {
    $stmt = $pdo->prepare("
        SELECT pr.*,
               COUNT(DISTINCT prr.id) as response_count,
               COUNT(DISTINCT CASE WHEN prr.is_admin_response = 1 THEN prr.id END) as admin_response_count,
               MAX(CASE WHEN prr.is_admin_response = 1 THEN prr.created_at END) as last_admin_response
        FROM prayer_requests pr
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.member_id = ?
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
    ");
} else {
    // Fallback query without admin response column
    $stmt = $pdo->prepare("
        SELECT pr.*,
               COUNT(DISTINCT prr.id) as response_count,
               0 as admin_response_count,
               NULL as last_admin_response
        FROM prayer_requests pr
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.member_id = ?
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
    ");
}
$stmt->execute([$_SESSION['user_id']]);
$my_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get community requests (based on privacy level)
$stmt = $pdo->prepare("
    SELECT pr.*, m.full_name, m.first_name,
           COUNT(DISTINCT prr.id) as response_count,
           MAX(CASE WHEN prr.member_id = ? THEN 1 ELSE 0 END) as i_responded
    FROM prayer_requests pr
    JOIN members m ON pr.member_id = m.id
    LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
    WHERE pr.member_id != ? 
    AND pr.privacy_level IN ('members', 'public')
    AND pr.status = 'active'
    GROUP BY pr.id
    ORDER BY pr.is_urgent DESC, pr.created_at DESC
    LIMIT 20
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$community_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Requests - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .dashboard-container {
            margin-top: 2rem;
        }

        .dashboard-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .dashboard-card h5 {
            color: var(--bs-body-color, #2c3e50);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .alert-success {
            border-radius: var(--bs-border-radius, 12px);
            border: none;
            background-color: var(--bs-success, #d4edda);
            color: var(--bs-body-color, #155724);
        }

        .request-item {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 10px);
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--bs-primary, #667eea);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .request-meta {
            font-size: 0.875rem;
            color: var(--bs-secondary, #6c757d);
        }

        .badge {
            border-radius: var(--bs-border-radius, 8px);
            padding: 0.5rem 0.75rem;
            font-weight: 500;
        }

        .request-card {
            border-left: 4px solid var(--bs-primary, #667eea);
            margin-bottom: 1rem;
        }
        .urgent-request {
            border-left-color: #dc3545;
        }
        .answered-request {
            border-left-color: #28a745;
            opacity: 0.8;
        }
        .closed-request {
            border-left-color: #6c757d;
            opacity: 0.6;
        }
        .response-count {
            background: #e9ecef;
            border-radius: 15px;
            padding: 2px 8px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container dashboard-container">
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Create New Request -->
            <div class="col-md-4">
                <div class="dashboard-card">
                    <h5><i class="bi bi-plus-circle"></i> Submit New Request</h5>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="personal">Personal</option>
                                    <option value="family">Family</option>
                                    <option value="health">Health</option>
                                    <option value="work">Work</option>
                                    <option value="ministry">Ministry</option>
                                    <option value="community">Community</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="privacy_level" class="form-label">Privacy Level</label>
                                <select class="form-select" id="privacy_level" name="privacy_level">
                                    <option value="private">Private (Only Me)</option>
                                    <option value="members" selected>Members (All Church Members)</option>
                                    <option value="public">Public (Everyone)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_urgent" name="is_urgent">
                                    <label class="form-check-label" for="is_urgent">
                                        Mark as Urgent
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous">
                                    <label class="form-check-label" for="is_anonymous">
                                        Submit Anonymously
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" name="create_request" class="btn btn-primary">
                                <i class="bi bi-send"></i> Submit Request
                            </button>
                        </form>
                </div>
            </div>

            <!-- My Requests -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="bi bi-person-circle"></i> My Requests (<?php echo count($my_requests); ?>)</h5>
                        <?php if (empty($my_requests)): ?>
                            <p class="text-muted text-center">You haven't submitted any requests yet.</p>
                        <?php else: ?>
                            <?php foreach ($my_requests as $request): ?>
                                <div class="card request-card <?php 
                                    echo $request['is_urgent'] ? 'urgent-request' : '';
                                    echo $request['status'] === 'answered' ? ' answered-request' : '';
                                    echo $request['status'] === 'closed' ? ' closed-request' : '';
                                ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="card-title">
                                                    <?php echo htmlspecialchars($request['title']); ?>
                                                    <?php if ($request['is_urgent']): ?>
                                                        <span class="badge bg-danger ms-1">Urgent</span>
                                                    <?php endif; ?>
                                                    <span class="badge bg-secondary ms-1"><?php echo ucfirst($request['status']); ?></span>
                                                </div>
                                            <div class="text-end">
                                                <small class="text-muted"><?php echo date('M j, Y', strtotime($request['created_at'])); ?></small>
                                                <br>
                                                <span class="response-count">
                                                    <i class="bi bi-heart"></i> <?php echo $request['response_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <p class="card-text mt-2"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-tag"></i> <?php echo ucfirst($request['category']); ?> |
                                                <i class="bi bi-eye"></i> <?php echo ucfirst($request['privacy_level']); ?>
                                            </small>
                                            
                                            <?php if ($request['status'] === 'active'): ?>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-success" onclick="updateStatus(<?php echo $request['id']; ?>, 'answered')">
                                                        <i class="bi bi-check-circle"></i> Mark Answered
                                                    </button>
                                                    <button class="btn btn-outline-secondary" onclick="updateStatus(<?php echo $request['id']; ?>, 'closed')">
                                                        <i class="bi bi-x-circle"></i> Close
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($request['status'] === 'answered' && $request['answered_description']): ?>
                                            <div class="mt-2 p-2 bg-light rounded">
                                                <small><strong>Answer:</strong> <?php echo nl2br(htmlspecialchars($request['answered_description'])); ?></small>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($request['admin_response_count'] > 0): ?>
                                            <div class="mt-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="showAdminResponses(<?php echo $request['id']; ?>)">
                                                    <i class="bi bi-chat-dots"></i> View Admin Response<?php echo $request['admin_response_count'] > 1 ? 's' : ''; ?> (<?php echo $request['admin_response_count']; ?>)
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Responses Modal -->
    <div class="modal fade" id="adminResponsesModal" tabindex="-1" aria-labelledby="adminResponsesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminResponsesModalLabel">
                        <i class="bi bi-chat-dots"></i> Admin Responses
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="adminResponsesContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function updateStatus(requestId, status) {
        let description = '';
        if (status === 'answered') {
            description = prompt('Please describe how this request was answered:');
            if (!description) return;
        }

        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="request_id" value="${requestId}">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="answered_description" value="${description}">
            <input type="hidden" name="update_status" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }

    function showAdminResponses(requestId) {
        const modal = new bootstrap.Modal(document.getElementById('adminResponsesModal'));
        const content = document.getElementById('adminResponsesContent');

        // Show loading spinner
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;

        modal.show();

        // Fetch admin responses
        fetch('ajax/get_admin_responses.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'request_id=' + requestId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                if (data.responses.length === 0) {
                    html = '<p class="text-muted text-center">No admin responses yet.</p>';
                } else {
                    data.responses.forEach(response => {
                        const responseTypeColors = {
                            'support': 'success',
                            'guidance': 'primary',
                            'prayer': 'info',
                            'referral': 'warning',
                            'follow_up': 'secondary',
                            'resolved': 'success'
                        };
                        const badgeColor = responseTypeColors[response.response_type] || 'secondary';

                        html += `
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-${badgeColor}">${response.response_type.replace('_', ' ').toUpperCase()}</span>
                                        <small class="text-muted">${new Date(response.created_at).toLocaleDateString()}</small>
                                    </div>
                                    <p class="card-text">${response.comment.replace(/\n/g, '<br>')}</p>
                                    <small class="text-muted">
                                        <i class="bi bi-person-badge"></i> Church Administration
                                    </small>
                                </div>
                            </div>
                        `;
                    });
                }
                content.innerHTML = html;
            } else {
                content.innerHTML = '<p class="text-danger">Error loading responses.</p>';
            }
        })
        .catch(error => {
            content.innerHTML = '<p class="text-danger">Error loading responses.</p>';
        });
    }
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
